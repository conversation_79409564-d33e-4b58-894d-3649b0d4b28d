<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class Filter extends Component
{
    public $type;
    public $properties;
    public $filterSearchSlug;

    /**
     * Create a new component instance.
     */
    public function __construct($type, $properties = [],  $filterSearchSlug = "")
    {
        $this->type = $type;
        $this->properties = $properties;
        $this->filterSearchSlug = $filterSearchSlug;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.filter');
    }
}
