<?php

namespace App\View\Components;

use Closure;
use Illuminate\Contracts\View\View;
use Illuminate\View\Component;

class PropertiesMap extends Component
{
    /**
     * Create a new component instance.
     */
    public $properties;
    public $type;

    public function __construct($properties = [], $type = '')
    {
        // Initialize properties and type
        $this->properties = $properties;
        $this->type = $type;
    }

    /**
     * Get the view / contents that represent the component.
     */
    public function render(): View|Closure|string
    {
        return view('components.properties-map');
    }
}
