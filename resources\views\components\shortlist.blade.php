<div>
    @php
        $locale = App::getLocale();
        $favorites = [];
        if (isset($_COOKIE['popfav']) && !empty($_COOKIE['popfav'])) {
            if (strpos($_COOKIE['popfav'], ',')) {
                $favorites = array_map('intval', explode(',', $_COOKIE['popfav']));
            } else {
                $favorites = array((int) $_COOKIE['popfav']);
            }
        }
    @endphp

    <a id="ic-favBTN" href="{{ URL::to($locale.'/'.Cms::getSlugByTagName('Shortlist')) }}" class="ic-button {{ isset($favorites) && !empty($favorites) ? '' : 'shortlist-empty' }}">
        <i class="fas fa-heart"></i>
        <em>{!! Translate::t('favorites') !!}</em> (<span class="shortlistCount">{{ isset($favorites) && !empty($favorites) ? count($favorites) : 0 }}</span>)
    </a>
</div>
