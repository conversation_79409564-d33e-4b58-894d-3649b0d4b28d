<div id="ic-orderBy">
    <input type="hidden" name="prop_ids" value="{{ isset($_REQUEST['prop_ids']) && !empty($_REQUEST['prop_ids']) ? is_array($_REQUEST['prop_ids']) ? implode(',', $_REQUEST['prop_ids']) : $_REQUEST['prop_ids'] : '' }}">
    <input type="hidden" name="getOptions" value="{{ json_encode(Request::all()) }}">
    <div class="ic-QSitem">
        <select class="jq-select null order-by {{ isset($ajaxLoad) && !empty($ajaxLoad) ? 'ajaxLoad' : '' }}" title="Order by" name="order" id="order_by" onchange="sortOrdering(this);">
            <option class="" {{ (Request::input('order_by') == 'own,-1') ? 'selected' : '' }} value="own,-1">{{ Translate::t('order by relevance') }}</option>
            <option class="" {{ (Request::input('order_by') == 'recommended') ? 'selected' : '' }} value="recommended">{{ Translate::t('recommended') }}</option>
            <option class="" {{ (Request::input('order_by') == 'created_at,-1'  || Request::input('order_by') == '') ? 'selected' : '' }} value="created_at,-1">{{ Translate::t('published descending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'created_at,1') ? 'selected' : '' }} value="created_at,1">{{ Translate::t('published ascending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'updated,-1') ? 'selected' : '' }} value="updated,-1">{{ Translate::t('recently updated') }}</option>
            <option class="" {{ (Request::input('order_by') == 'updated,1') ? 'selected' : '' }} value="updated,1">{{ Translate::t('updated ascending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'current_price,1') ? 'selected' : '' }} value="current_price,1">{{ Translate::t('price ascending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'current_price,-1') ? 'selected' : '' }} value="current_price,-1">{{ Translate::t('price descending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'bedrooms,1') ? 'selected' : '' }} value="bedrooms,1">{{ Translate::t('bedrooms ascending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'bedrooms,-1') ? 'selected' : '' }} value="bedrooms,-1">{{ Translate::t('bedrooms descending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'plot,-1') ? 'selected' : '' }} value="plot,-1">{{ Translate::t('plot size ascending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'plot,-1') ? 'selected' : '' }} value="plot,-1">{{ Translate::t('plot size descending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'built,-1') ? 'selected' : '' }} value="built,-1">{{ Translate::t('built size ascending') }}</option>
            <option class="" {{ (Request::input('order_by') == 'built,-1') ? 'selected' : '' }} value="built,-1">{{ Translate::t('built size descending') }}</option>
        </select>
    </div>
</div>