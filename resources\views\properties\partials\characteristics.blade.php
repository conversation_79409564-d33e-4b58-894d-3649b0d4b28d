<ul class="ic-filled ic-featList">
    @if(isset($property['type_one']) && !empty($property['type_one']))
        <li>
            {{ isset($property['type_one']) ? $property['type_one'] : '' }}
        </li>
    @endif

    @if(isset($property['location']) && !empty($property['location']))
        <li>
            {{ isset($property['location']) ? $property['location'] : '' }}
        </li>
    @endif

    @if(isset($property['property_features']['categories']) && !empty($property['property_features']['categories']) && in_array('new_construction', $property['property_features']['categories']))
        <li>
            {{ Translate::t('new build') }}
        </li>
    @endif

    @if(isset($property['property_features']['pool']['pool_private']) && !empty($property['property_features']['pool']['pool_private']))
        <li>
            {{ Translate::t('private pool') }}
        </li>
    @endif

    @if(isset($property['co2']) && !empty($property['co2']))
        <li>
            {{ Translate::t('consume co2 pending') }}
        </li>
        <li>
            {{ Translate::t('emissions co2 pending') }}
        </li>
    @endif

    @if(isset($property["property_features"]["categories"]) && !empty($property["property_features"]["categories"]) && array_filter($property["property_features"]["categories"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["categories"] as $key => $value)
            @if(isset($value))
                @php $filter_array[] = Translate::t($value); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $category)
            <li>
                {{ $category }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["setting"]) && !empty($property["property_features"]["setting"]) && array_filter($property["property_features"]["setting"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["setting"] as $key => $value)
            @if(isset($value))
                @php $filter_array[] = Translate::t($value); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $setting)
            <li>
                {{ $setting }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["orientation"]) && !empty($property["property_features"]["orientation"]) && array_filter($property["property_features"]["orientation"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["orientation"] as $key => $value)
            @if(isset($value))
                @php $filter_array[] = Translate::t($value); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["views"]) && !empty($property["property_features"]["views"]) && array_filter($property["property_features"]["views"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["views"] as $key => $value)
            @if(isset($value))
                @php $filter_array[] = Translate::t($value); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["condition"]) && !empty($property["property_features"]["condition"]) && array_filter($property["property_features"]["condition"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["condition"] as $key => $value)
            @if(isset($value))
                @php $filter_array[] = Translate::t($value); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["kitchen"]) && !empty($property["property_features"]["kitchen"]) && array_filter($property["property_features"]["kitchen"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["kitchen"] as $key => $value)
            @if(isset($value) && !empty($value) && $key != 'quantity')
                @if(gettype($value) == 'boolean')
                    @php $filter_array[] = Translate::t($key); @endphp
                @else
                    @php $filter_array[$key][] = Translate::t($value); @endphp
                @endif
            @endif
        @endforeach


        @foreach ($filter_array as $key => $value)
            @if(is_array($value))
                @foreach ($value as $item)
                    <li>
                        {{ Translate::t($key) ." : ". Translate::t($item) }}
                    </li>
                @endforeach
            @else
                <li>
                    {{ $value }}
                </li>
            @endif
        @endforeach
    @endif
    @if(isset($property["property_features"]["custom_fields"]) && !empty($property["property_features"]["custom_fields"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["custom_fields"] as $key => $custom_field)
            @foreach ($custom_field as $category)
                @if(isset($category['value']) && !empty($category['value']) && $category['value'] == "true")
                    @php $filter_array[] = Translate::t($category['key']); @endphp
                @endif
            @endforeach
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach

        @if(isset($property["property_features"]['custom_fields']["distance"]) && !empty($property["property_features"]['custom_fields']["distance"]))
            @foreach ($property["property_features"]['custom_fields']["distance"] as $key => $value)
                @if(isset($property["property_features"]['custom_fields']["distance"][$key]) && !empty($property["property_features"]['custom_fields']["distance"][$key]) && isset($property["property_features"]['custom_fields']["distance"][$key]["value"]) && !empty($property["property_features"]['custom_fields']["distance"][$key]["value"]))
                    @php
                        $features_key = isset($property["property_features"]['custom_fields']["distance"][$key]['defaultKey']) && !empty($property["property_features"]['custom_fields']["distance"][$key]['defaultKey']) ? Translate::t($property["property_features"]['custom_fields']["distance"][$key]['defaultKey']) : Translate::t($property["property_features"]['custom_fields']["distance"][$key]['key']);
                    @endphp
                    <li>
                        {{ $features_key . ": " . (isset($property["property_features"]['custom_fields']["distance"][$key]["value"]) && !empty($property["property_features"]['custom_fields']["distance"][$key]["value"]) ? $property["property_features"]['custom_fields']["distance"][$key]["value"] : '') }}
                        {{ isset($property["property_features"]['custom_fields']["distance"][$key]["unit"]) && !empty($property["property_features"]['custom_fields']["distance"][$key]["unit"]) ? $property["property_features"]['custom_fields']["distance"][$key]["unit"] : '' }}
                    </li>
                @endif
            @endforeach
        @endif
    @endif

    @if(isset($property["property_features"]["kitchen"]["quantity"]) && !empty($property["property_features"]["kitchen"]["quantity"]))
        <li>
            {{ Translate::t('kitchen quantity') . ' : ' .$property["property_features"]["kitchen"]["quantity"] }}
        </li>
    @endif

    @if(isset($property["property_features"]["utility"]) && !empty($property["property_features"]["utility"]) && array_filter($property["property_features"]["utility"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["utility"] as $key => $value)
            @if(isset($value) && !empty($value))
                @php $filter_array[] = Translate::t($key); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["living_room"]) && !empty($property["property_features"]["living_room"]) && array_filter($property["property_features"]["living_room"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["living_room"] as $key => $value)
            @if(isset($value) && !empty($value))
                @php $filter_array[] = Translate::t($key); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property['toilets']) && !empty($property['toilets']))
        <li>
            {{ Translate::t('toilets') . " : " . $property['toilets'] }}
        </li>
    @endif

    @if(isset($property['living_rooms']) && !empty($property['living_rooms']))
        <li>
            {{ Translate::t('living_rooms') . " : " . $property['living_rooms'] }}
        </li>
    @endif

    @if(isset($property['bath_tubs']) && !empty($property['bath_tubs']))
        <li>
            {{ Translate::t('bath_tubs') . " : " . $property['bath_tubs'] }}
        </li>
    @endif

    @if(isset($property['bidet']) && !empty($property['bidet']))
        <li>
            {{ Translate::t('bidet') . " : " . $property['bidet'] }}
        </li>
    @endif

    @if(isset($property['jaccuzi_bath']) && !empty($property['jaccuzi_bath']))
        <li>
            {{ Translate::t('jaccuzi_bath') . " : " . $property['jaccuzi_bath'] }}
        </li>
    @endif

    @if(isset($property['corner_shower']) && !empty($property['corner_shower']))
        <li>
            {{ Translate::t('corner_shower') . " : " . $property['corner_shower'] }}
        </li>
    @endif

    @if(isset($property['sink']) && !empty($property['sink']))
        <li>
            {{ Translate::t('sink') . " : " . $property['sink'] }}
        </li>
    @endif

    @if(isset($property['double_sink']) && !empty($property['double_sink']))
        <li>
            {{ Translate::t('double_sink') . " : " . $property['double_sink'] }}
        </li>
    @endif

    @if(isset($property['walk_in_shower']) && !empty($property['walk_in_shower']))
        <li>
            {{ Translate::t('walk_in_shower') . " : " . $property['walk_in_shower'] }}
        </li>
    @endif

    @if(isset($property['en_suite']) && !empty($property['en_suite']))
        <li>
            {{ Translate::t('en_suite') . " : " . $property['en_suite'] }}
        </li>
    @endif

    @if(isset($property['wheelchair_accesible_shower']) && !empty($property['wheelchair_accesible_shower']))
        <li>
            {{ Translate::t('wheelchair_accesible_shower') . " : " . $property['wheelchair_accesible_shower'] }}
        </li>
    @endif

    @if(isset($property['hairdryer']) && !empty($property['hairdryer']))
        <li>
            {{ Translate::t('hairdryer') . " : " . $property['hairdryer'] }}
        </li>
    @endif

    @if(isset($property['beds']) && !empty($property['beds']))
        @php $filter_array = array(); @endphp

        @foreach ($property['beds'] as $key => $bed)
            @foreach ($bed as $value)
                @php $filter_array[$key][] = $value['x'] . " x " . $value['y']; @endphp
            @endforeach
        @endforeach

        @foreach ($filter_array as $key => $value)
            @foreach ($value as $item)
                <li>
                    {{ Translate::t($key) . " : " . $item }}
                </li>
            @endforeach
        @endforeach
    @endif

    @if(isset($property['property_features']['rooms']) && !empty($property['property_features']['rooms']))
        @foreach ($property['property_features']['rooms'] as $room)
            @if(isset($room['name'][strtoupper(App::getLocale())]) && !empty($room['name'][strtoupper(App::getLocale())]))
                <li>
                    {{ $room['name'][strtoupper(App::getLocale())] . " : " . $room['size'] . " m²" }}
                </li>
            @endif
        @endforeach
    @endif

    @if(isset($property["property_features"]["security"]) && !empty($property["property_features"]["security"]) && array_filter($property["property_features"]["security"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["security"] as $key => $value)
            @if(isset($value) && !empty($value))
                @php $filter_array[] = Translate::t($key); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["furniture"]) && !empty($property["property_features"]["furniture"]) && array_filter($property["property_features"]["furniture"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["furniture"] as $key => $value)
            @if(isset($value) && !empty($value))
                @php $filter_array[] = Translate::t($key); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["climate_control"]) && !empty($property["property_features"]["climate_control"]) && array_filter($property["property_features"]["climate_control"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["climate_control"] as $key => $value)
            @if(isset($value) && !empty($value))
                @php $filter_array[] = Translate::t($key); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["parking"]) && !empty($property["property_features"]["parking"]) && array_filter($property["property_features"]["parking"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["parking"] as $key => $value)
            @if(isset($value) && !empty($value))
                @if($value == 1)
                    @php $filter_array[] = Translate::t($key); @endphp
                @else
                    @php
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value;
                    @endphp
                @endif
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                @if(is_array($value))
                    {{ $value['key'] . ': ' . $value['value'] }}
                @else
                    {{ $value }}
                @endif
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["garden"]) && !empty($property["property_features"]["garden"]) && array_filter($property["property_features"]["garden"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["garden"] as $key => $value)
            @if(gettype($value) == 'boolean')
                @php $filter_array[] = Translate::t($key); @endphp
            @else
                @php
                    $filter_array[$key]['key'] = Translate::t($key);
                    $filter_array[$key]['value'] = $value;
                @endphp
            @endif
        @endforeach


        @foreach ($filter_array as $value)
            <li>
                @if(is_array($value))
                    {{ $value['key'] . ' : ' . $value['value'] . ' m²' }}
                @else
                    {{ $value }}
                @endif
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["pool"]) && !empty($property["property_features"]["pool"]) && array_filter($property["property_features"]["pool"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["pool"] as $key => $value)
            @if(gettype($value) == 'boolean')
                @php $filter_array[] = Translate::t($key); @endphp
            @else
                @php
                    if(isset($value['pool_size_length']) && !empty($value['pool_size_length']) && isset($value['pool_size_width']) && !empty($value['pool_size_width']) && isset($value['dephth_length']) && !empty($value['dephth_length']) && isset($value['dephth_width']) && !empty($value['dephth_width'])) {
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value['pool_size_length'] . ' m² x ' . $value['pool_size_width'] . ' m² x '. $value['dephth_length'] . ' m² x '. $value['dephth_width'] . ' m²';
                    } else if(isset($value['pool_size_length']) && !empty($value['pool_size_length']) && isset($value['pool_size_width']) && !empty($value['pool_size_width'])) {
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value['pool_size_length'] . ' m² x ' . $value['pool_size_width'] . ' m²';
                    } else if(isset($value['dephth_length']) && !empty($value['dephth_length']) && isset($value['dephth_width']) && !empty($value['dephth_width'])) {
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value['dephth_length'] . ' m² x '. $value['dephth_width'] . ' m²';
                    } else if(isset($value['pool_size_length']) && !empty($value['pool_size_length'])) {
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value['pool_size_length'] . ' m²';
                    } else if(isset($value['pool_size_width']) && !empty($value['pool_size_width'])) {
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value['pool_size_width'] . ' m²';
                    } else if(isset($value['dephth_length']) && !empty($value['dephth_length'])) {
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value['dephth_length'] . ' m²';
                    } else if(isset($value['dephth_width']) && !empty($value['dephth_width'])) {
                        $filter_array[$key]['key'] = Translate::t($key);
                        $filter_array[$key]['value'] = $value['dephth_width'] . ' m²';
                    }
                @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                @if(is_array($value))
                    {{ $value['key'] . ' : ' . $value['value'] }}
                @else
                    {{ $value }}
                @endif
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["leisure"]) && !empty($property["property_features"]["leisure"]) && array_filter($property["property_features"]["leisure"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["leisure"] as $key => $value)
            @if(isset($value) && !empty($value))
                @php $filter_array[] = Translate::t($key); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["features"]) && !empty($property["property_features"]["features"]) && array_filter($property["property_features"]["features"]))
        @php $filter_array = array(); @endphp

        @foreach ($property["property_features"]["features"] as $key => $value)
            @if(isset($value) && !empty($value))
                @php $filter_array[] = Translate::t($key); @endphp
            @endif
        @endforeach

        @foreach ($filter_array as $value)
            <li>
                {{ $value }}
            </li>
        @endforeach
    @endif

    @if(isset($property["property_features"]["distances"]) && !empty($property["property_features"]["distances"]))
        @foreach ($property["property_features"]["distances"] as $key => $value)
            @if(isset($property["property_features"]["distances"][$key]) && !empty($property["property_features"]["distances"][$key]) && isset($property["property_features"]["distances"][$key]["value"]) && !empty($property["property_features"]["distances"][$key]["value"]))
                <li>
                    {{ Translate::t($key) . ": " . (isset($property["property_features"]["distances"][$key]["value"]) && !empty($property["property_features"]["distances"][$key]["value"]) ? $property["property_features"]["distances"][$key]["value"] : '') }}
                    {{ isset($property["property_features"]["distances"][$key]["unit"]) && !empty($property["property_features"]["distances"][$key]["unit"]) ? $property["property_features"]["distances"][$key]["unit"] : '' }}
                </li>
            @endif
        @endforeach
    @endif
</ul>
