<!DOCTYPE html>
<html lang="en">

<head>
    @php
        $params = App::bound('params') ? App::make('params') : [];
        $custom_settings = Cms::custom_settings();
    @endphp

    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <meta name="theme-color" content="#2c404f">

    {!! Sitehelper::register_meta_tags($params) !!}

    <title>{{ Sitehelper::get_title($params) ?? $page }}</title>

    {!! Sitehelper::set_favicon($params) !!}

    {!! Sitehelper::registerCanonicalTag($params, isset($page_custom_settings['canonical_link']) ? $page_custom_settings['canonical_link'] : '') !!}

    <link rel="preconnect" href="https://fonts.gstatic.com">

    @stack('head-scripts')

    @if(file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
        @vite([
            'resources/css/app.css',
        ])
    @endif

    {{-- <link rel="stylesheet" href="{{ asset('uploads/temp/cms-variables.css') }}"> --}}

    @stack('custom-styles')

    {!! Sitehelper::get_script($params) !!}
</head>

<body>
    {!! Sitehelper::get_body_script($params) !!}
    <x-header />

    {{ $slot }}

    <x-footer />

    @if(file_exists(public_path('build/manifest.json')) || file_exists(public_path('hot')))
        @vite([
            'resources/js/app.js',
        ])
    @endif
    {{-- {!! Functions::renderReCaptchaJs(true) !!} --}}
    
    @stack('custom-scripts')
</body>

</html>
