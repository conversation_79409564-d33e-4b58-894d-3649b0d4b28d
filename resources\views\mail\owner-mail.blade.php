@php
    $settings = Cms::settings();
    $logo = isset($settings['header']['logo']['name']) ? ('https://images.optima-crm.com/cms_settings/' . config("params.template") . '/' . $settings['header']['logo']['name']) : ''
@endphp

<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">

<head>
	<style type="text/css">
		/* -------------------------------------
		GLOBAL
        ------------------------------------- */
		* {
			margin: 0;
			padding: 0;
		}

		* {
			font-family: "Helvetica Neue", "Helvetica", Helvetica, Arial, sans-serif;
		}

		img {
			max-width: 100%;
		}

		.collapse {
			margin: 0;
			padding: 0;
		}

		body {
			-webkit-font-smoothing: antialiased;
			-webkit-text-size-adjust: none;
			width: 100% !important;
			height: 100%;
		}


		/* -------------------------------------
		ELEMENTS
        ------------------------------------- */
		a {
			color: #2BA6CB;
		}

		.btn {
			text-decoration: none;
			color: var(--bs-white);
			background-color: #666;
			padding: 10px 16px;
			font-weight: bold;
			margin-right: 10px;
			text-align: center;
			cursor: pointer;
			display: inline-block;
		}

		p.callout {
			padding: 15px;
			background-color: #ECF8FF;
			margin-bottom: 15px;
		}

		.callout a {
			font-weight: bold;
			color: #2BA6CB;
		}

		table.social {
			/* 	padding:15px; */
			background-color: #ebebeb;

		}

		.social .soc-btn {
			padding: 3px 7px;
			font-size: 12px;
			margin-bottom: 10px;
			text-decoration: none;
			color: var(--bs-white);
			font-weight: bold;
			display: block;
			text-align: center;
		}

		a.fb {
			background-color: #3B5998 !important;
		}

		a.tw {
			background-color: #1daced !important;
		}

		a.gp {
			background-color: #DB4A39 !important;
		}

		a.ms {
			background-color: var(--bs-black) !important;
		}

		.sidebar .soc-btn {
			display: block;
			width: 100%;
		}

		/* -------------------------------------
		HEADER
        ------------------------------------- */
		table.head-wrap {
			width: 100%;
		}

		.header.container table td.logo {
			padding: 15px;
		}

		.header.container table td.label {
			padding: 15px;
			padding-left: 0px;
		}


		/* -------------------------------------
		BODY
        ------------------------------------- */
		table.body-wrap {
			width: 100%;
		}


		/* -------------------------------------
		FOOTER
        ------------------------------------- */
		table.footer-wrap {
			width: 100%;
			clear: both !important;
		}

		.footer-wrap .container td.content p {
			border-top: 1px solid rgb(215, 215, 215);
			padding-top: 15px;
		}

		.footer-wrap .container td.content p {
			font-size: 10px;
			font-weight: bold;

		}


		/* -------------------------------------
		TYPOGRAPHY
        ------------------------------------- */
		h1,
		h2,
		h3,
		h4,
		h5,
		h6 {
			font-family: "HelveticaNeue-Light", "Helvetica Neue Light", "Helvetica Neue", Helvetica, Arial, "Lucida Grande", sans-serif;
			line-height: 1.1;
			margin-bottom: 15px;
			color: var(--bs-black);
		}

		h1 small,
		h2 small,
		h3 small,
		h4 small,
		h5 small,
		h6 small {
			font-size: 60%;
			color: #6f6f6f;
			line-height: 0;
			text-transform: none;
		}

		h1 {
			font-weight: 200;
			font-size: 44px;
		}

		h2 {
			font-weight: 200;
			font-size: 37px;
		}

		h3 {
			font-weight: 500;
			font-size: 27px;
		}

		h4 {
			font-weight: 500;
			font-size: 23px;
		}

		h5 {
			font-weight: 900;
			font-size: 17px;
		}

		h6 {
			font-weight: 900;
			font-size: 14px;
			text-transform: uppercase;
			color: #444;
		}

		.collapse {
			margin: 0 !important;
		}

		p,
		ul {
			margin-bottom: 10px;
			font-weight: normal;
			font-size: 14px;
			line-height: 1.6;
		}

		p.lead {
			font-size: 17px;
		}

		p.last {
			margin-bottom: 0px;
		}

		ul li {
			margin-left: 5px;
			list-style-position: inside;
		}

		/* -------------------------------------
		SIDEBAR
        ------------------------------------- */
		ul.sidebar {
			background: #ebebeb;
			display: block;
			list-style-type: none;
		}

		ul.sidebar li {
			display: block;
			margin: 0;
		}

		ul.sidebar li a {
			text-decoration: none;
			color: #666;
			padding: 10px 16px;
			/* 	font-weight:bold; */
			margin-right: 10px;
			/* 	text-align:center; */
			cursor: pointer;
			border-bottom: 1px solid #777777;
			border-top: 1px solid var(--bs-white);
			display: block;
			margin: 0;
		}

		ul.sidebar li a.last {
			border-bottom-width: 0px;
		}

		ul.sidebar li a h1,
		ul.sidebar li a h2,
		ul.sidebar li a h3,
		ul.sidebar li a h4,
		ul.sidebar li a h5,
		ul.sidebar li a h6,
		ul.sidebar li a p {
			margin-bottom: 0 !important;
		}



		/* ---------------------------------------------------
		RESPONSIVENESS
		Nuke it from orbit. It's the only way to be sure.
        ------------------------------------------------------ */

		/* Set a max-width, and make it display as block so it will automatically stretch to that width, but will also shrink down on a phone or something */
		.container {
			display: block !important;
			max-width: 600px !important;
			margin: 0 auto !important;
			/* makes it centered */
			clear: both !important;
		}

		/* This should also be a block element, so that it will fill 100% of the .container */
		.content {
			padding: 15px;
			max-width: 600px;
			margin: 0 auto;
			display: block;
		}

		/* Let's make sure tables in the content area are 100% wide */
		.content table {
			width: 100%;
		}


		/* Odds and ends */
		.column {
			width: 300px;
			float: left;
		}

		.column tr td {
			padding: 15px;
		}

		.column-wrap {
			padding: 0 !important;
			margin: 0 auto;
			max-width: 600px !important;
		}

		.column table {
			width: 100%;
		}

		.social .column {
			width: 280px;
			min-width: 279px;
			float: left;
		}

		/* Be sure to place a .clear element after each set of columns, just to be safe */
		.clear {
			display: block;
			clear: both;
		}


		/* -------------------------------------------
		PHONE
		For clients that support media queries.
		Nothing fancy.
        -------------------------------------------- */
		@media only screen and (max-width: 600px) {

			a[class="btn"] {
				display: block !important;
				margin-bottom: 10px !important;
				background-image: none !important;
				margin-right: 0 !important;
			}

			div[class="column"] {
				width: auto !important;
				float: none !important;
			}

			table.social div[class="column"] {
				width: auto !important;
			}

		}
	</style>	
	<meta name="viewport" content="width=device-width" />

	<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
</head>

<body style="background-color: #ffffff; margin: 0; padding: 0;">
	<!-- BODY -->
	<table width="100%" cellpadding="0" cellspacing="0" style="background-color: #f8f8f8; padding: 20px;">
		<tr>
			<td></td>
			<td width="600" style="background-color: #ffffff; border-radius: 8px; box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);">

				<div style="padding: 20px;">
					<h2 style="color: #333; font-family: Arial, sans-serif;">client details</h2>
					<p style="font-family: Arial, sans-serif; color: #555;">
						<strong>Client Name: </strong> {{ (isset($model->first_name) ? $model->first_name : 'N/A') . ' ' . (isset($model->last_name) ? $model->last_name : "") }}<br /><br />
						<strong>Client Phone: </strong> {{ isset($model->phone) && !empty($model->phone) ? $model->phone : 'N/A' }}<br /><br />
						<strong>Client email: </strong> {{ isset($model->email) && !empty($model->email) ? $model->email : 'N/A' }}<br /><br />
						<strong>Client language: </strong> {{ App::getLocale() }}<br /><br />
						@if (isset($model->reference) && !empty($model->reference))
							<strong>Reference: </strong> {{ $model->reference }}<br /><br />
						@endif
						@if (isset($model->other_reference) && !empty($model->other_reference))
							<strong>Other Reference: </strong> {{ $model->other_reference }}<br /><br />
						@endif
						@if (isset($model->bathrooms) && !empty($model->bathrooms))
							<strong>Bathrooms: </strong> {{ $model->bathrooms }}<br /><br />
						@endif
						@if (isset($model->bedrooms) && !empty($model->bedrooms))
							<strong>Bedrooms: </strong> {{ $model->bedrooms }}<br /><br />
						@endif
						@if (isset($model->buy_price_from) && !empty($model->buy_price_from))
							<strong>Price: </strong> {{ $model->buy_price_from }}<br /><br />
						@endif
						@if ($model->arrival_date != '')
							<strong>Arrival date: </strong> {{ $model->arrival_date }}<br /><br />
						@endif
						@if ($model->departure_date != '')
							<strong>Departure date: </strong> {{ $model->departure_date }}<br /><br />
						@endif
						@if ($model->guests != '')
							<strong>Number guests: </strong> {{ $model->guests }}<br /><br />
						@endif
						@if (isset($model->message) && !empty($model->message) && isset($hideMessage) && $hideMessage)
							<strong>{{ isset($other_information) && !empty($other_information) ? "Other Information" : 'Message From Client' }}: </strong><br /><span style="padding-left: 20px; display: block;">{{ $model->message }}</span><br /><br />
						@endif
						@if (isset($property) && !empty($property)) :
							{{ $property  }};
						@endif;

					</p>
				</div>

			</td>
			<td></td>
		</tr>
	</table><!-- /BODY -->

	<!-- FOOTER -->
	<table width="100%" cellpadding="0" cellspacing="0" style="background-color: #ffffff; padding: 20px;">
		<tr>
			<td></td>
			<td width="600" style="background-color: #ffffff;">

				<div style="padding: 10px; text-align: center;">
					<img style="height: 80px;" src="{{ $logo }}" alt="Company Logo" />
					<h6 style="color: #888; font-family: Arial, sans-serif; margin: 10px 0 0;">&copy; {{ date('Y') }} {{ isset($settings['general_settings']['site_title']) ? $settings['general_settings']['site_title'] : 'Your Company Name' }}</h6>
				</div>

			</td>
			<td></td>
		</tr>
	</table><!-- /FOOTER -->

</body>
</html>