<?php

namespace App\Http\Controllers;

use Daxit\OptimaClass\Components\Sitehelper;
use Daxit\OptimaClass\Components\Translate;
use Daxit\OptimaClass\Helpers\Cms;
use Daxit\OptimaClass\Helpers\CommercialProperties;
use Daxit\OptimaClass\Helpers\ContactUs;
use Daxit\OptimaClass\Helpers\Functions;
use Daxit\OptimaClass\Requests\ContactUsRequest;
use Daxit\OptimaClass\Service\ParamsContainer;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Redirect;
use Illuminate\View\View;
use Illuminate\View\ViewException;

class SiteController extends Controller
{
    public function Index(Request $request): View
    {
        if($request->has('skip-cache')) {
            self::skipCache();
        }

        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $home = Cms::getSlugByTagName('home') == 'tag-not-found' ? 'home' : Cms::getSlugByTagName('home');

        $page_data = Cms::getPage(['slug' => !empty($slugs) ? $slugs[0] : $home, 'lang' => App::getLocale()]);

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $homeSliders = array_reverse(Cms::postTypes("Home Slider"));

        $ourKeyAdvantages = array_reverse(Cms::postTypes("Our Key Advantages"));

        $buySellItems = array_reverse(Cms::postTypes("Buy sell items ") ?? []);

        $categoryWiseProperties = array_reverse(cms::postTypes("category-wise-properties") ?? []);
        
        $removePriceRange = PropertiesController::setPriceCondition(false);

        $featuredProperties = CommercialProperties::findAll(1, 12, ["featured" => true, "sale" => 1, "remove_count" => true], ['created_at' => '-1'], ['image_size' => 1600]) ?? [];

        PropertiesController::setPriceCondition($removePriceRange);

        return view(isset($page_data['view_path']) && !empty($page_data['view_path']) ? $page_data['view_path'] : "site.index", compact('page_data', 'homeSliders', 'ourKeyAdvantages', 'buySellItems', 'categoryWiseProperties', 'locale', 'featuredProperties'));
    }

    public function PrivacyPolicy(Request $request): View
    {
        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        return view($page_data['view_path'], compact('page_data'));
    }

    public function CookiesPolicy(Request $request): View
    {
        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        return view($page_data['view_path'], compact('page_data'));
    }

    public function ContactUs(Request $request): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        return view($page_data['view_path'], compact('page_data', 'locale'));
    }

    public function contactUsStore(ContactUsRequest $request): RedirectResponse
    {
        $data = $request->validated();

        $contactUs = new ContactUs();
        $contactUs->fill($data);
        $response = $contactUs->sendMail();

        if($response) {
            $message = Translate::t('thank you for your message!');
        } else {
            $message = Translate::t('getting errors while creating a lead!');
        }

        return Redirect::back()->with(['success' => $response, 'message' => $message]);
    }

    public function AboutUs(Request $request): View
    {
        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $agentsTeam = array_reverse(Cms::postTypes("Agent Team"));

        $associationSections = array_reverse(Cms::postTypes("Association Section"));

        return view($page_data['view_path'], compact('page_data', 'agentsTeam', 'associationSections'));
    }

    public function AftersalesCare(Request $request): View
    {
        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        return view($page_data['view_path'], compact('page_data'));
    }

    public function FrequentQuestions(Request $request): View
    {
        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        return view($page_data['view_path'], compact('page_data'));
    }

    public function BuyingGuide(Request $request): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $buyingGuideTimelines = array_reverse(Cms::postTypes("Buying Guide Timeline", null, null, 25));

        return view($page_data['view_path'], compact('page_data', 'buyingGuideTimelines', 'locale'));
    }

    public function InformationAreaActivities(Request $request): View
    {
        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        return view($page_data['view_path'], compact('page_data'));
    }

    public function Blog(Request $request): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $blogs = array_reverse(Cms::postTypes('post', null, null, -1));

        return view($page_data['view_path'], compact('page_data', 'blogs', 'locale'));
    }

    public function BlogDetail(Request $request, $title): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => $locale]) : [];

        $post = Cms::pageBySlug($title, strtoupper($locale), null, 'post');

        ParamsContainer::addAppInstance(['page_data' => $page_data, 'post' => $post]);

        $blog = Sitehelper::blog();

        $related_posts = $this->relatedPosts($blog, $post, 3);

        return view($page_data['view_path'], compact('page_data', 'post', 'related_posts', 'title', 'locale'));
    }

    private function relatedPosts($allPosts, $currentPost, $noOfPosts): array
    {
        $related_posts = [];

        if (isset($allPosts) && !empty($allPosts) && isset($currentPost) && !empty($currentPost)) {
            for ($i = 0; $i < count($allPosts) && count($related_posts) < $noOfPosts; $i++) {
                if (isset($currentPost['categories']) && !empty($currentPost['categories'])) {
                    if (isset($allPosts[$i]['categories']) && !empty($allPosts[$i]['categories'])) {
                        $count = array_intersect($allPosts[$i]['categories'], $currentPost['categories']);
                        if (count($count) > 0 && $allPosts[$i]['title'] != $currentPost['title']) {
                            $related_posts[$i] = $allPosts[$i];
                        }
                    }
                } else {
                    if ($allPosts[$i]['title'] != $currentPost['title']) {
                        $related_posts[$i] = $allPosts[$i];
                    }
                }
            }
        }
        return $related_posts;
    }

    public function AreaInfo(Request $request): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $areaInfos = array_reverse(Cms::postTypes("Area Info"));

        return view($page_data['view_path'], compact('page_data', 'areaInfos', 'locale'));
    }

    public function TipsFromHomebuyers(Request $request): View
    {
        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        return view($page_data['view_path'], compact('page_data'));
    }

    public function Page(Request $request): mixed
    {
        $_GET["random"] = true;

        $slugs = array_slice($request->segments(), 1);

        if ($slugs) {
            $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];
        }

        if (!isset($page_data) || empty(array_filter($page_data))) {
            return Redirect::route('/' . App::getLocale());
        }

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        try {
            $viewPath = isset($page_data['view_path']) && !empty($page_data['view_path']) ? $page_data['view_path'] : 'site.page';
            return view($viewPath, compact('page_data'));
        } catch (ViewException $exception) {
            return Redirect::route('/' . App::getLocale());
        }
    }

    public function skipCache()
    {
        $webroot = public_path();

        $dirname = Functions::directory();
        Functions::deleteDirectory($dirname);

        Cms::cmsRules();
        Cms::Slugs('page');

        Functions::deleteDirectory($webroot . '/assets');

        if (!File::exists($webroot . '/assets')) {
            File::makeDirectory($webroot . '/assets', 0755, true);
        }

        $settings = Cms::settings();

        $css = ':root {
            --main_color_1: #100E62;
            --main_color_2: #EF1616;
            --main_color_black: #000000;
            --main_color_white: #FFFFFF;
        }';

        if (!File::exists($webroot . '/user_country')) {
            File::makeDirectory($webroot . '/user_country', 0755, true);
        }

        $files = glob($webroot . '/user_country/' . "*.json");

        if (isset($files) && !empty($files)) {
            foreach ($files as $file) {
                if (File::exists($file) && time() - filemtime($file) > 24 * 3600) {
                    unlink($file);
                }
            }
        }

        if (isset($settings['frontend_settings'])) { // For creating cms-variablescss file add setings in general settings css front-end settings.
            $css = ':root {';
            foreach ($settings['frontend_settings'] as $frontend_settings) {
                if ($frontend_settings['type'] == 'css_variable') {
                    $css .= '$' . strtolower($frontend_settings['setting_key']) . ':' . $frontend_settings['setting_value'] . ';';
                }
            }

            $css .= '
                $main_color_black: #000000;
                $main_color_white: #FFFFFF;
                ';
            $css .= '}';
        }

        if (isset($settings['additional_css'])) { // for creating sass file add styling in CRM textarea styling tab
            $scss = ltrim($settings['additional_css']);
        }

        if (isset($scss) && !empty($scss)) { //always first check sass file as default css is creating on line 586.
            $css_file = Functions::directory() . 'cms-variables.css';
            if (!File::exists($css_file)) {
                file_put_contents($css_file, $scss);
            }
        } elseif (isset($css) && !empty($css)) { // if sass file not exist then css file will create from forontend settings or default css settings.
            $css_file = Functions::directory() . 'cms-variables.css';
            if (!File::exists($css_file)) {
                file_put_contents($css_file, $css);
            }
        }

        return "cleared successfully";
    }
    /**
     * Handle the Testimonials request.
     */
    public function Testimonials(Request $request): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $page_data = !empty($slugs) ? Cms::getPage(['slug' => $slugs[0], 'lang' => App::getLocale()]) : [];

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $testimonials = array_reverse(Cms::postTypes("testimonials"));

        return view($page_data['view_path'], compact('page_data', 'testimonials', 'locale'));
    }
}