<?php

namespace App\Http\Controllers;

use App\Helpers\Helper;
use Daxit\OptimaClass\Components\Translate;
use Daxit\OptimaClass\Components\Urlhelper;
use Daxit\OptimaClass\Helpers\Cms;
use Daxit\OptimaClass\Helpers\CommercialProperties;
use Daxit\OptimaClass\Helpers\Properties;
use Daxit\OptimaClass\Service\ParamsContainer;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\View as FacadesView;
use Illuminate\View\View;

class PropertiesController extends Controller
{
    public function Index(Request $request): mixed
    {
        if ($request->ajax()) {
            $_GET = $request->all();
        }

        $locale = App::getLocale();

        if($request->has('prop_ids')){
            $_GET['prop_ids'] = str_replace(['[', ']', '"', ' '], '', $request->input('prop_ids'));
        }

        $slugs = array_slice($request->segments(), 1);

        $slug = isset($slugs[0]) && !empty($slugs) ? $slugs[0] : '';

        $page_data = Cms::getPage(['slug' => $slug, 'lang' => App::getLocale()]);

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $query = [];

        $page = $request->input('page', 1);

        $page_size = $request->input('per-page', 12);

        if (!isset($_GET['order_by']) || (isset($_GET['order_by']) && empty($_GET['order_by']))) {
            $sort = ['created_at' => -1, 'reference' => 1];
        }

        if (isset($_GET['order_by']) && !empty($_GET['order_by'])) {

            $order = explode(',', $_GET['order_by']);

            if (isset($order[0]) && $order[0] == "recommended") {

                // $_GET['own'] = true;
                $_GET['orderby'] = 'updateDESC';
                $sort = ['updateDESC' => 1];
                // $query['own']=true; //after removing change to vender folder
                // $sort = ['created_at' => 1, 'reference' =>  1];
                $sort = ['featured' => -1, 'reference' => 1];

            } else {
                $sort = [$order[0] => (int) $order[1], 'created_at' => (int) $order[1], 'reference' => (int) $order[1]];
            }

        }

        $custom_settings = Cms::custom_settings($page_data['custom_settings']);

        if (empty(array_filter($page_data))) {

            $other_pages = ["Area Info"];

            foreach ($other_pages as $key_other => $other_page) {

                if (empty(array_filter($page_data))) {

                    $result = self::processPageDataAndCustomSettings((!empty($slugs) ? $slugs[0] : ''), $other_page);
                    $page_data = $result['page_data'] ?? [];
                    $custom_settings = $result['custom_settings'] ?? [];

                }
            }
        }

        self::processCustomSettings($custom_settings);

        self::processFiltering();

        $_GET['sale'] = 1;

        $_GET['remove_count'] = true;

        if(Cms::getSlugByTagName('search-by-map') == $slug){
            $postParams = ["params" => array_filter($request->all())];
        } else {
            $postParams = ["path" => URL::to($locale . '/' . $slug)];
        }

        $removePriceRange = PropertiesController::setPriceCondition(false);

        $commercialProperties = CommercialProperties::findAll($page, $page_size, $query, $sort, ['image_size' => 1600]) ?? [];

        PropertiesController::setPriceCondition($removePriceRange);

        $commercialProperties = self::setProperiesCount($commercialProperties, $page_size);

        $properties = Helper::paginate($commercialProperties, $page, $page_size, $postParams);

        if ($request->ajax()) {
            $result = "";
            if (isset($properties) && $properties->isNotEmpty()) {
                foreach ($properties as $key => $property) {
                    $result .= FacadesView::make("components.properties.partials.property-card", ["property" => $property])->render();
                }
            }

            $pagination = FacadesView::make("properties.partials.pagination-view", ["paginator" => $properties])->render();

            return response()->json([
                "result" => isset($result) && !empty($result) && $result != "" ? $result : Translate::t('no properties'),
                "pagination" => $pagination
            ]);
        }

        return view(isset($page_data['view_path']) && !empty($page_data['view_path']) ? $page_data['view_path'] : 'properties.index', compact('page_data', 'custom_settings', 'properties', 'locale', 'slug'));
    }

    public function PropertyView(Request $request, $title): mixed
    {
        $locale = App::getLocale();

        $lang = strtoupper($locale);

        $slugs = array_slice($request->segments(), 1);

        $slug = isset($slugs[0]) && !empty($slugs) ? $slugs[0] : '';

        $page_data = Cms::getPage(['slug' => $slug, 'lang' => $locale]);

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $page_slug = Cms::getSlugByTagName('Rent Details');

        $holiday_slug = Cms::getSlugByTagName('Holiday Details');

        $title = explode( '_', $title);

        if (isset($slug) && $slug == $page_slug) {
            $_GET['rent'] = 1;
            $_GET['lt_rental'] = 1;
            $transaction = "lt_rental";
        } elseif (isset($slug) && $slug == $holiday_slug) {
            $_GET['holiday_rental'] = 1;
            $transaction = "st_rental";
        } else {
            $_GET['sale'] = 1;
            $transaction = "sale";
        }

        $property = CommercialProperties::findOne(end($title),["image_size" => 1600]);

        ParamsContainer::addAppInstance(['property' => $property]);

        if (!isset($property["id"])) {
            return Redirect::to(url()->previous() != url()->current() ? url()->previous() : '/' . strtolower($locale))->with(['success' => false, 'message' => Translate::t("property is not found")]);
        }

        if ($request->input('rent') == 1) {
            $params = ['rent' => 'true'];
        } else {
            $params = ['sale' => 'true'];
        }

        if (isset($property['slug_all'][$lang]) && !empty($property['slug_all'][$lang]) && $request->input('title') !== Urlhelper::getPropertyTitle($property)) {
            return Urlhelper::getPropertyUrl($property);
        }

        $favorites = [];
        if (isset($_COOKIE['popfav'])) {
            if (strpos($_COOKIE['popfav'], ',')) {
                $favorites = explode(',', $_COOKIE['popfav']);
            } else {
                $favorites = $_COOKIE['popfav'];
            }
        }

        $similar_properties = $this->similarProperties($property, $params, 3);

        $agent_id = "";
        if (isset($property["own"]) && !empty($property["own"]) && isset($property["agency"]) && !empty($property["agency"]) && !empty(config('params.agency')) && $property["agency"] == config('params.agency')) {
            $agent_id = isset($property['listing_agent_id']) ? $property['listing_agent_id'] : config('params.default_agent_id');
        } else {
            $agent_id = config('params.default_agent_id');
        }

        $agent_details = Properties::getAgent($agent_id);
        $agent_details["_id"] = $agent_id;

        return view($page_data['view_path'], compact('page_data', 'property', 'transaction', "favorites", "locale", "similar_properties", "agent_details"));
    }

    /**
     * Function to process and handle page data and custom settings.
     *
     * @param string $slug The slug to retrieve the page data.
     * @param string $other_page The other page identifier.
     * @return mixed The processed page data or null if any required data is invalid.
     */

    public function processPageDataAndCustomSettings($slug, $other_page): mixed
    {
        $page_data = Cms::pageBySlug($slug, App::getLocale(), null, $other_page);

        if (empty(array_filter($page_data))) {
            return null;
        }

        App::instance('params', new ParamsContainer(['page_data' => $page_data]));

        $custom_settings = Cms::custom_settings($page_data['custom_settings']);

        self::processCustomSettings($custom_settings);

        $page_data['view_path'] = isset($page_data['view_path']) && !empty($page_data['view_path']) ? $page_data['view_path'] : 'properties/index';

        return ['page_data' => $page_data, 'custom_settings' => $custom_settings];
    }

    public function processCustomSettings($custom_settings): void
    {
        if (isset($custom_settings['lg_by_key']) && !empty($custom_settings['lg_by_key'])) {
            $_GET['lg_by_key'] = explode(',', $custom_settings['lg_by_key']);
        }

        if (isset($custom_settings['custom_category']) && !empty($custom_settings['custom_category'])) {
            $_GET['categories'] = explode(',', $custom_settings['custom_category']);
        }

        if (isset($custom_settings["type"]) && !empty($custom_settings["type"])) {
            $_GET["type"] = (isset($custom_settings["type"]) && !empty($custom_settings["type"]) && is_array($custom_settings["type"])) ? $custom_settings["type"] : explode(',', $custom_settings["type"]);
        }

        if (isset($custom_settings["city"]) && !empty($custom_settings["city"])) {
            $_GET["city"] = (isset($custom_settings["city"]) && !empty($custom_settings["city"]) && is_array($custom_settings["city"])) ? $custom_settings["city"] : explode(',', $custom_settings["city"]);
        }

        if (isset($custom_settings["country"]) && !empty($custom_settings["country"])) {
            $_GET["country"] = (isset($custom_settings["country"]) && !empty($custom_settings["country"]) && is_array($custom_settings["country"])) ? $custom_settings["country"] : explode(',', $custom_settings["country"]);
        }

        if (isset($custom_settings["location"]) && !empty($custom_settings["location"])) {
            $_GET["location"] = (isset($custom_settings["location"]) && !empty($custom_settings["location"]) && is_array($custom_settings["location"])) ? $custom_settings["location"] : explode(',', $custom_settings["location"]);
        }

        if (isset($custom_settings["features"]) && !empty($custom_settings["features"])) {
            $_GET["features"] = (isset($custom_settings["features"]) && !empty($custom_settings["features"]) && is_array($custom_settings["features"])) ? $custom_settings["features"] : explode(',', $custom_settings["features"]);
        }

        if (isset($custom_settings["featured_properties"]) && !empty($custom_settings["featured_properties"])) {
            $_GET["featured"] = (bool) $custom_settings["featured_properties"];
        }

        if (isset($custom_settings["status"]) && !empty($custom_settings["status"])) {
            $_GET["status"] = (isset($custom_settings["status"]) && !empty($custom_settings["status"]) && is_array($custom_settings["status"])) ? $custom_settings["status"] : explode(',', $custom_settings["status"]);
        }

        if (isset($custom_settings['transaction_type']) && $custom_settings['transaction_type']) {

            $_GET['transaction_type'] = $custom_settings['transaction_type'];

            if (in_array($_GET['transaction_type'], ["st_rental", "st-rental"]) || in_array($_GET['transaction_type'], ["st_rental", "st-rental", "holiday_rental", "holiday-rental"])) {
                $_GET['rent'] = 1;
                $_GET['st_rental'] = 1;
            } elseif (in_array($_GET['transaction_type'], ["lt_rental", "lt-rental"])) {
                $_GET['rent'] = 1;
                $_GET['lt_rental'] = 1;
            } elseif (in_array($_GET['transaction_type'], ["new_development", "new-development"])) {
                $_GET["sale"] = 1;
                $_GET['project'] = 1;
            } else {
                $_GET["sale"] = 1;
            }
        } else {
            $custom_settings['transaction_type'] = isset($custom_settings['transaction_type']) ? $custom_settings['transaction_type'] : 'sale';
            $_GET["sale"] = 1;
        }

        if (isset($custom_settings["property"]) && !empty($custom_settings["property"])) {
            $_GET["property"] = isset($custom_settings["property"]) && !empty($custom_settings["property"]) ? $custom_settings["property"] : "";
        }

        if(!isset($_GET['order_by']) || (isset($_GET['order_by']) && empty($_GET['order_by']))) {
            $_GET["properties_count"] = isset($custom_settings["properties_count"]) && !empty($custom_settings["properties_count"]) ? $custom_settings["properties_count"] : "";
        }

    }

    public static function setProperiesCount($commercialProperties, $page_size = 12): mixed
    {
        if(isset($_GET['properties_count']) && !empty($_GET['properties_count'])) {
            $properties_count = (int) $_GET['properties_count'] ?? 0;
            $commercialProperties["total"] = $properties_count ?? $commercialProperties["total"];
            $commercialProperties["pages"] = (int)ceil((int) $_GET['properties_count'] / $page_size) ?? $commercialProperties["pages"];            
        }
        return $commercialProperties;
    }

    public function CustomCategoryProperties(Request $request): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $slug = isset($slugs[0]) && !empty($slugs) ? $slugs[0] : '';

        $page_data = Cms::getPage(['slug' => $slug, 'lang' => App::getLocale()]);

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $query = [];

        $page = $request->input('page', 1);

        $page_size = $request->input('per-page', 12);

        if (!isset($_GET['order_by']) || (isset($_GET['order_by']) && empty($_GET['order_by']))) {
            $sort = ['created_at' => -1, 'reference' => 1];
        }

        if (isset($_GET['order_by']) && !empty($_GET['order_by'])) {

            $order = explode(',', $_GET['order_by']);

            if (isset($order[0]) && $order[0] == "recommended") {

                // $_GET['own'] = true;
                $_GET['orderby'] = 'updateDESC';
                $sort = ['updateDESC' => 1];
                // $query['own']=true; //after removing change to vender folder
                // $sort = ['created_at' => 1, 'reference' =>  1];
                $sort = ['featured' => -1, 'reference' => 1];

            } else {
                $sort = [$order[0] => (int) $order[1], 'created_at' => (int) $order[1], 'reference' => (int) $order[1]];
            }

        }

        $custom_settings = Cms::custom_settings($page_data['custom_settings']);

        if (empty(array_filter($page_data))) {

            $other_pages = ["category-wise-properties", "footer-type-properties"];

            foreach ($other_pages as $key_other => $other_page) {

                if (empty(array_filter($page_data))) {

                    $result = $this->processPageDataAndCustomSettings((!empty($slugs) ? $slugs[0] : ''), $other_page);
                    $page_data = $result['page_data'] ?? [];
                    $custom_settings = $result['custom_settings'] ?? [];

                }
            }
        }

        $this->processCustomSettings($custom_settings);

        self::processFiltering();

        $_GET['remove_count'] = true;

        if(Cms::getSlugByTagName('search-by-map') == $slug){
            $postParams = ["params" => $_GET];
        } else {
            $postParams = ["path" => URL::to($locale . '/' . $slug)];
        }

        $removePriceRange = PropertiesController::setPriceCondition(false);

        $commercialProperties = CommercialProperties::findAll($page, $page_size, $query, $sort, ['image_size' => 1600]) ?? [];

        PropertiesController::setPriceCondition($removePriceRange);

        $commercialProperties = self::setProperiesCount($commercialProperties, $page_size);

        $properties = Helper::paginate($commercialProperties, $page, $page_size, $postParams);

        return view($page_data['view_path'], compact('page_data', 'custom_settings', 'properties'));
    }

    public function similarProperties($property, $params = [], $noOfProps = 5): array
    {
        if (isset($_GET['sale']) && $_GET['sale'] == 1) {
            $price = isset($property['price']) ? $property['price'] : 0;
        } elseif (isset($_GET['rent']) && $_GET['rent'] == 1) {
            if (isset($property['rental_season_data']) && !empty($property['rental_season_data'])) {
                foreach ($property['rental_season_data'] as $season_data) {
                    $price = (isset($season_data['total_per_month']) && !empty($season_data['total_per_month']) ? $season_data['total_per_month'] : 0);
                    if ((isset($season_data['period_from']) && !empty($season_data['period_from']) && (int) strtotime("now") >= (int) strtotime($season_data['period_from'])) && (isset($season_data['period_to']) && !empty($season_data['period_to']) && (int) strtotime("now") <= (int) strtotime($season_data['period_to']))) {
                        $price = (isset($season_data['total_per_month']) && !empty($season_data['total_per_month']) ? $season_data['total_per_month'] : 0);
                    }
                }
            }
        } elseif (isset($_GET['holiday_rental']) && $_GET['holiday_rental'] == 1) {
            if (isset($property['rental_seasons']) && !empty($property['rental_seasons'])) {
                foreach ($property['rental_seasons'] as $season_data) {
                    $price = (isset($season_data['price_per_day']) && !empty($season_data['price_per_day']) ? $season_data['price_per_day'] : 0);
                    if ((isset($season_data['period_from']) && !empty($season_data['period_from']) && (int) strtotime("now") >= (int) strtotime($season_data['period_from'])) && (isset($season_data['period_to']) && !empty($season_data['period_to']) && (int) strtotime("now") <= (int) strtotime($season_data['period_to']))) {
                        $price = (isset($season_data['price_per_day']) && !empty($season_data['price_per_day']) ? $season_data['price_per_day'] : 0);
                    }
                }
            }
        }

        if (!isset($price)) {
            $price = 0;
        }

        $price_max = $price + ((25 * $price) / 100);
        $price_min = $price - ((10 * $price) / 100);
        if ($price_max > 0) {
            if (isset($_GET['sale']) && $_GET['sale'] == 1) {
                $_GET['price_from'] = $price_min;
                $_GET['price_to'] = $price_max;
            } else {
                $_GET['rental_price_from'] = $price_min;
                $_GET['rental_price_to'] = $price_max;
            }
        }

        if (isset($property['type'])) {
            $_GET['type'] = [$property['type']];
        }

        if (isset($property['location_key'])) {
            $_GET['location'] = [$property['location_key']];
        }

        if (isset($property['city_key'])) {
            $_GET['city'] = [$property['city_key']];
        }

        $removePriceRange = PropertiesController::setPriceCondition(false);

        $similar_properties = CommercialProperties::findAll(1, $noOfProps, [], ['own' => '-1'], ['image_size' => 900]);

        PropertiesController::setPriceCondition($removePriceRange);

        $props = [];
        if (isset($similar_properties['docs']) && !empty($similar_properties['docs']) && isset($property) && !empty($property)) {
            for ($i = 0; $i < count($similar_properties['docs']) && count($props) < $noOfProps; $i++) {
                if (isset($property['id']) && isset($similar_properties['docs'][$i]['id']) && $similar_properties['docs'][$i]['id'] != $property['id']) {
                    $props['docs'][$i] = $similar_properties['docs'][$i];
                }
            }
        }

        return $props;
    }

    public function Shortlist(Request $request): View
    {
        $locale = App::getLocale();

        $slugs = array_slice($request->segments(), 1);

        $slug = isset($slugs[0]) && !empty($slugs) ? $slugs[0] : '';

        $page = $request->input('page', 1);

        $page_size = $request->input('per-page', 12);

        $sort = [];

        if (!isset($_GET['order_by']) || (isset($_GET['order_by']) && empty($_GET['order_by']))) {
            $sort = ['created_at' => -1, 'reference' => 1];
        }

        $page_data = Cms::getPage(['slug' => $slug, 'lang' => $locale]);

        ParamsContainer::addAppInstance(['page_data' => $page_data]);

        $favorites = 'null';

        if (isset($_COOKIE['popfav']) && !empty($_COOKIE['popfav'])) {
            if (strpos($_COOKIE['popfav'], ',')) {
                $favorites = array_map('intval', explode(',', $_COOKIE['popfav']));
            } else {
                $favorites = (int) $_COOKIE['popfav'];
            }
        }

        $_GET['favorite_ids'] = $favorites;

        if(Cms::getSlugByTagName('search-by-map') == $slug){
            $postParams = ["params" => $_GET];
        } else {
            $postParams = ["path" => URL::to($locale . '/' . $slug)];
        }

        $removePriceRange = PropertiesController::setPriceCondition(false);

        $commercialProperties = CommercialProperties::findAll($page, $page_size, "", $sort, ["image_size" => 900]) ?? [];

        PropertiesController::setPriceCondition($removePriceRange);
        
        $commercialProperties = self::setProperiesCount($commercialProperties, $page_size);

        $properties = Helper::paginate($commercialProperties, $page, $page_size, $postParams);

        return view($page_data['view_path'], compact('page_data', 'locale', 'properties'));
    }

    public static function processFiltering(): void
    {

        if (isset($_GET['bed']) && $_GET['bed'] == 'bedrooms' && isset($_GET['bedrooms']) && !empty($_GET['bedrooms'])) {
            $_GET['max_bed'] = $_GET['bedrooms'];
        }

        if (isset($_GET['surface']) && $_GET['surface'] == 'surface-min' && isset($_GET['surface-area']) && !empty($_GET['surface-area'])) {
            $_GET['min_built'] = $_GET['surface-area'];
        }

        if (isset($_GET['features']) && !empty($_GET['features'])) {
            $_GET['cp_features'] = [];

            foreach ($_GET['features'] as $feature) {

                if ($feature == 'parking') {
                    $_GET['cp_features']['parking'][] = ['parking.communal_garage' => (bool) 'true'];
                    $_GET['cp_features']['parking'][] = ['parking.covered' => (bool) 'true'];
                    $_GET['cp_features']['parking'][] = ['parking.garage' => (bool) 'true'];
                    $_GET['cp_features']['parking'][] = ['parking.ground_floor_parking' => (bool) 'true'];
                    $_GET['cp_features']['parking'][] = ['parking.parking' => (bool) 'true'];
                    $_GET['cp_features']['parking'][] = ['parking.parking_communal' => (bool) 'true'];
                    $_GET['cp_features']['parking'][] = ['parking.private' => (bool) 'true'];
                    $_GET['cp_features']['parking'][] = ['parking.underground' => (bool) 'true'];
                }

                if ($feature == 'pool') {
                    $_GET['cp_features']['pool'][] = ['pool.childrens_pool' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.fenced_pool' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.pool_heated' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.infinity_pool' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.plunge_pool' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.pool' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.pool_indoor' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.pool_private' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.soler_heated_pool' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.freshwater_saltwater' => (bool) 'true'];
                    $_GET['cp_features']['pool'][] = ['pool.pool_communal' => (bool) 'true'];
                }

                if ($feature == 'golf') {
                    $_GET['cp_features']['categories'][] = ['categories.golf' => (bool) 'true'];
                }

                if ($feature == 'luxury') {
                    $_GET['cp_features']['categories'][] = ['categories.luxury' => (bool) 'true'];
                }

                if ($feature == 'investment') {
                    $_GET['cp_features']['categories'][] = ['categories.investment' => (bool) 'true'];
                }

                if ($feature == 'lift') {
                    $_GET['cp_features']['features'][] = ['features.lift_elevator' => (bool) 'true'];
                }

                if ($feature == 'terrace') {
                    $_GET['cp_features']['features'][] = ['features.private_terrace' => (bool) 'true'];
                }

                if ($feature == 'garage') {
                    $_GET['cp_features']['parking'][] = ['parking.garage' => (bool) 'true'];
                }

                if ($feature == 'storage') {
                    $_GET['cp_features']['features'][] = ['features.storage_room' => (bool) 'true'];
                }

                if ($feature == 'garden') {
                    $_GET['cp_features']['garden'][] = ['garden.almond_grove' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.fruit_trees_citrus' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.communal_garden' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.garden_communal' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.easy_maintenance' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.fenced_garden' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.irrigation_rights' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.landscaped' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.landscaped_garden' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.lawn' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.olive_grove' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.outdoor_dining' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.playground' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.plenty_of_water' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.pool_house' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.private_garden' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.garden_private' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.shade_control' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.shade_control_system' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.tropical_garden' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.fruit_trees_tropical' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.vegetable' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.veranda' => (bool) 'true'];
                    $_GET['cp_features']['garden'][] = ['garden.vineyard' => (bool) 'true'];
                }

                if ($feature == 'sea') {
                    $_GET['cp_features']['views'][] = ['views.sea' => (bool) 'true'];
                }

                if ($feature == 'beach') {
                    $_GET['cp_features']['views'][] = ['views.beach' => (bool) 'true'];
                }

                if
                 ($feature == 'mountain-view') {
                    $_GET['cp_features']['views'][] = ['views.mountain' => (bool) 'true'];
                }

                if ($feature == 'gated-complex') {
                    $_GET['cp_features']['security'][] = ['security.gated_complex' => (bool) 'true'];
                }

                if ($feature == 'tennis-court') {
                    $_GET['cp_features']['leisures'][] = ['leisures.tennis_court' => (bool) 'true'];
                }

                if ($feature == 'jacuzzi') {
                    $_GET['cp_features']['leisures'][] = ['leisures.jacuzzi' => (bool) 'true'];
                }

                if ($feature == 'air-conditioning') {
                    $_GET['cp_features']['climate_control'][] = ['climate_control.air_conditioning' => (bool) 'true'];
                }

                if ($feature == 'beachfront') {
                    $_GET['cp_features']['settings'][] = ['settings.beachfront' => (bool) 'true'];
                }

                if ($feature == 'new-built') {
                    $_GET['new_built'] = true;
                }

                if ($feature == 'renovation-project') {
                    $_GET['cp_features']['furniture'][] = ['furniture.fully_furnished' => (bool) 'true'];
                }

            }
        }

        if(isset($_GET["orientations"]) && !empty($_GET["orientations"])){
            $_GET['cp_features']['orientations'][] = ['orientations.' . $_GET["orientations"] => (bool) 'true'];
        }

        if(isset($_GET["property"]) && !empty($_GET["property"])){
            if(is_array($_GET["property"])){
                foreach ($_GET["property"] as $key => $value) {
                    $_GET[$value] = true;
                }
            }else{
                $_GET[$_GET["property"]] = true;
            }
        }

        //for only SDE
        if(config('params.agency', '') == '676596cea919895f3d0c432f'){
            if(isset($_GET["type"]) && !empty($_GET["type"])) {
                if(is_array($_GET["type"]) && array_intersect($_GET["type"], [150, 143, 142])){
                    $_GET["type"] = array_unique(array_merge($_GET["type"], [150, 143, 142]));
                }else if(in_array($_GET["type"], [150, 143, 142])){
                    $_GET["type"] = [150, 143, 142];
                }
            }

            if(isset($_POST["type"]) && !empty($_POST["type"])) {
                if(is_array($_POST["type"]) && array_intersect($_POST["type"], [150, 143, 142])){
                    $_GET["type"] = array_unique(array_merge($_POST["type"], [150, 143, 142]));
                }else if(in_array($_POST["type"], [150, 143, 142])){
                    $_GET["type"] = [150, 143, 142];
                }
            }
        }
    }

    public static function setPriceCondition($removePriceRange): bool
    {
        if($removePriceRange){
            unset($_GET["price_from"], $_GET["price_to"]);
            request()->replace(request()->except(['price_from', 'price_to']));
            return false;
        }else{
            if (empty($_GET['price_from']) && empty($_GET['price_to'])) {
                $_GET['price_from'] = 200000;
                $_GET['price_to'] = 15000000;
    
                return true;
            }
            return false;
        }
    }
}
