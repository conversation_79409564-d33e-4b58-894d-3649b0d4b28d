<?php

use Daxit\OptimaClass\Middleware\LocaleMiddleware;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
        then: function () {
            $filePath = base_path('routes/site.php');
            if(!File::exists($filePath)){
                file_put_contents($filePath, "");
            }
            Route::middleware(['locale', 'web'])->group($filePath);
        },
    )
    ->withMiddleware(function (Middleware $middleware) {
        $middleware->use([
            LocaleMiddleware::class
        ]);
        $middleware->alias([
            'locale' => LocaleMiddleware::class
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        //
    })->create();
