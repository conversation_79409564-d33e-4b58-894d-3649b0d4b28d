<?php

namespace App\Console\Commands;

use App\Helpers\Sitemap;
use Illuminate\Console\Command;

class SiteMapCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:site-map';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $siteMap = new Sitemap();
        return $siteMap->Index();
    }
}
