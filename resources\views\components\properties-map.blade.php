@php
    use Daxit\OptimaClass\Helpers\CommercialProperties;
    use App\Http\Controllers\PropertiesController;
    use Illuminate\Support\Str;

    $locale = App::getLocale() == 'es' ? 'es_AR' : App::getLocale();
    $buy_slug = Cms::getSlugByTagName('Buy');
    $holiday_slug = Cms::getSlugByTagName('Holiday');
    $rent_slug = Cms::getSlugByTagName('Rent');

    $slugs = array_slice(request()->segments(), 1);
    $slug = isset($slugs[0]) && !empty($slugs[0]) && in_array([0], array($buy_slug, $rent_slug, $holiday_slug)) ? $slugs[0] : $buy_slug;

    $custom_settings = Cms::custom_settings();

    $_GET["latLang"] = 1;

    $removePriceRange = PropertiesController::setPriceCondition(false);

    $propertieslatlng = CommercialProperties::findAllWithLatLang(true, [], false,  "&selectedFields=1");

    PropertiesController::setPriceCondition($removePriceRange);

    unset($_GET["latLang"]);
    request()->replace(request()->except('latLang'));

    $markers = [];

    $properties_ids = [];
@endphp

@if (isset($propertieslatlng) && !empty($propertieslatlng) && is_array($propertieslatlng))
    @foreach ($propertieslatlng as $latlng)
        @php
            $latitude = isset($latlng['private_info_object'][config('params.agency')]['latitude']) && !empty($latlng['private_info_object'][config('params.agency')]['latitude']) ? $latlng['private_info_object'][config('params.agency')]['latitude'] : (isset($latlng['latitude']) && !empty($latlng['latitude']) ? floatval($latlng['latitude']) : "");

            $longitude = isset($latlng['private_info_object'][config('params.agency')]['longitude']) && !empty($latlng['private_info_object'][config('params.agency')]['longitude']) ? $latlng['private_info_object'][config('params.agency')]['longitude'] : (isset($latlng['longitude']) && !empty($latlng['longitude']) ? floatval($latlng['longitude']) : "");
            if(isset($latitude) && !empty($latitude) && isset($longitude) && !empty($longitude)){
                $markers[$latlng['_id']] = [$latitude, $longitude];
            }
        @endphp
    @endforeach
@endif

@if (!isset($markers) || (isset($markers) && empty($markers)))
    @php
        $locations = Sitehelper::getGEODataIfExistsProperties([], 'allow_location', 'key', 'value', ['latitude', 'longitude']);
        foreach ($locations as $location) {
            $latitude = isset($location['latitude']) && !empty($location['latitude']) ? floatval(Str::replace('\r', '', $location['latitude'])) : "";
            $longitude = isset($location['longitude']) && !empty($location['longitude']) ? floatval(Str::replace('\r', '', $location['longitude'])) : "";
            if(isset($latitude) && !empty($latitude) && isset($longitude) && !empty($longitude)){
                $markers[$location['option_key']] = [$latitude, $longitude];
            }
        }
        $map_search_type = "location";
    @endphp
@else
    @php
        $map_search_type = "prop_ids";
    @endphp
@endif
{{-- @dd($markers); --}}

@if (isset($properties) && $properties->isNotEmpty())
    @foreach ($properties as $prop)
        @php
            $properties_ids[] = $prop['_id'];
        @endphp
    @endforeach
@endif

@push('custom-styles')
    @vite('resources/css/properties-map.css')
@endpush

@if($type == 'vertical')
    <button id="search-map-btn" class="search-map-btn search-map" role="button" data-bs-toggle="modal" data-bs-target="#mapModal"><i class="fas fa-map-marker-alt"></i> {{ Translate::t("search by map") }}</button>
@else
    <div class="border-property">
        <div class="search-map"
            data-bs-toggle="modal" data-bs-target="#mapModal" role="button">
            <button class="map-img">
                <svg width="26" height="26" viewBox="0 0 26 38" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <g clip-path="url(#clip0_515_163)">
                        <path
                            d="M25.2752 12.9717C25.2752 19.7367 13.0004 37.2764 13.0004 37.2764C13.0004 37.2764 0.725586 19.7367 0.725586 12.9717C0.725586 6.2068 6.22034 0.723145 12.9997 0.723145C19.7791 0.723145 25.2745 6.20743 25.2745 12.9717H25.2752Z"
                            stroke="#181818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                        <path
                            d="M17.7839 17.7459C20.426 15.1094 20.426 10.8349 17.7839 8.19842C15.1418 5.56195 10.8581 5.56195 8.21596 8.19842C5.57386 10.8349 5.57386 15.1094 8.21596 17.7459C10.8581 20.3824 15.1418 20.3824 17.7839 17.7459Z"
                            stroke="#181818" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
                    </g>
                    <defs>
                        <clipPath id="clip0_515_163">
                            <rect width="26" height="38" fill="white" />
                        </clipPath>
                    </defs>
                </svg>
                <b class="">{{ Translate::t("search by map") }}</b>
            </button>
        </div>
    </div>
@endif

@push('head-scripts')
    <script>
        let markers = {!! json_encode($markers) !!};
        let lang = "{{ strtolower($locale) }}";
        const zoom = {{ ((isset($custom_settings['map_zoom']) ? $custom_settings['map_zoom'] : 10)) }};
        const map_center_lat = {{ (isset($custom_settings['map_center_lat']) ? $custom_settings['map_center_lat'] : 38.5640) }};
        const map_center_lng = {{ (isset($custom_settings['map_center_lng']) ? $custom_settings['map_center_lng'] : -0.6250) }};
        const map_search = {{ Request::input('map_search') ? Request::input('map_search') : "false" }};
        let properties_ids = {!! json_encode($properties_ids) !!};
        const map_id = "gh-map-marker";
        const reset_map_search = "{{ Translate::t('Reset map search')}}";
        const draw_map_search = "{{ Translate::t('draw your area on the map') }}";
        let map_search_type = "{{ $map_search_type }}";
    </script>

    <script src="https://maps.google.com/maps/api/js?libraries=marker,geometry&language={{ $locale }}{{ isset($custom_settings['map_key']) && !empty($custom_settings['map_key']) ? ("&key=" . $custom_settings['map_key']) : '' }}&loading=async" async defer></script>
@endpush

@push('custom-scripts')
    <script src="{{ asset('core/components/infocasa/files/themes/casaselect/js/markerclusterer.js') }}" defer></script>
    <script src="{{ asset('core/components/infocasa/files/themes/casaselect/js/draw-on-map.js') }}" defer></script>
    <script src="{{ asset('core/components/infocasa/files/themes/casaselect/js/map-script.js') }}" defer></script>
@endpush
