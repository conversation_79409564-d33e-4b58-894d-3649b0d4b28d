<?php

namespace App\Helpers;

use Cocur\Slugify\Slugify;
use Daxit\OptimaClass\Helpers\Cms;
use Daxit\OptimaClass\Helpers\Properties;
use Daxit\OptimaClass\Service\ParamsContainer;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Http;

class Sitemap
{
    public function Index()
    {
        Cms::setParams(); // to set config as config not loaded yet in rules due to early execution
        $rulesCms = Cms::cmsRules();
        $cmsModel = Cms::Slugs('page');

        $property_detail = self::getSlugByTagName('Sale Details');
        // $rent_detail = self::getSlugByTagName('Rent Details');
        // $holiday_detail = self::getSlugByTagName('Holiday Details');
        // $newDevelopment_detail = self::getSlugByTagName('New Development Details');
        $blog_details = self::getSlugByTagName('blog-detail');
        // $agent_details = self::getSlugByTagName('Agent Details');
        $blogs = $this->getAllBlogs();

        $PostType = ["Area Info", "category-wise-properties", "footer-type-properties"];
        $excludePages = ["content-page"];
        $siteMapArray = array();
        foreach ($cmsModel as $cmsKey => $cmsModelvalue) {
            foreach ($rulesCms as $ruleKey => $rulesValue) {
                if ($cmsModelvalue['slug'] == $rulesValue['pattern'] && !in_array($cmsModelvalue['slug'], $siteMapArray) && !in_array($cmsModelvalue['slug'], $excludePages)) {
                    if (isset($cmsModelvalue['slug_all']) && is_array($cmsModelvalue['slug_all'])) {
                        foreach ($cmsModelvalue['slug_all'] as $key => $value) {
                            if (!in_array($value, $siteMapArray)) {
                                $siteMapArray[] = $value;
                            }
                        }
                    }
                }
            }
        }
        $langs = Cms::languages();
        foreach ($langs as $lang) {
            $langg[] = $lang;
        }
        $properties_sale = $this->getCommercialPropertiesSale();
        // $properties_rent = $this->getCommercialPropertiesRent();
        // $properties_holiday = $this->getCommercialPropertiesHoliday();
        // $properties_newdevelop = $this->getCommercialPropertiesDevelopment();
        // $agent_info = Cms::postTypes('agent-team', null, null, 1000);

        $site = config("params.site-url");

        foreach ($langg as $lang) {
            $unique_url = array();
            $params = App::bound('params') ? App::make('params') : [];
            App::instance('params',(object) array_merge((array) $params, ["unique_url" => $unique_url]));
            $html = '<?xml version="1.0" encoding="UTF-8"?>';
            $html .= '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';

            foreach ($siteMapArray as $key => $value) {
                $html = $this->getUrlXml($value, $cmsModel, $lang, $site, $html, false, []);
            }
            foreach ($PostType as $key => $value) {
                // $post = Cms::postTypes($value);
                // foreach ($post as $subKey => $subValue) {
                $html = $this->getUrlXml($value, $cmsModel, $lang, $site, $html, true, [], false, $unique_url);
                // }
            }
            $html = $this->getUrlXml($blog_details, $cmsModel, $lang, $site, $html, false, $blogs, false, $unique_url);
            // $html = $this->getUrlXml($agent_details, $cmsModel, $lang, $site, $html, false, $agent_info, false, $unique_url);
            $html = $this->getUrlXml($property_detail, $cmsModel, $lang, $site, $html, false, $properties_sale, true, $unique_url);
            // $html = $this->getUrlXml($rent_detail, $cmsModel, $lang, $site, $html, false, $properties_rent, true, $unique_url);
            // $html = $this->getUrlXml($holiday_detail, $cmsModel, $lang, $site, $html, false, $properties_holiday, true, $unique_url);
            // $html = $this->getUrlXml($newDevelopment_detail, $cmsModel, $lang, $site, $html, false, $properties_newdevelop, true, $unique_url);

            $html .= '</urlset>';
            file_put_contents(public_path() . '/sitemap-' . strtolower($lang['key']) . '.xml', $html);
        }
        $index_html = $this->getSitemapIndex($site);
        file_put_contents(public_path() . '/sitemap_index.xml', $index_html);
        die('done');
    }

    function getUrlXml($slug, $slugModel, $lang, $site, $html, $type = false, $subModel = [], $commercialProperty = false, $unique_url = [])
    {
        $params = App::bound('params') ? App::make('params') : [];
        $unique_url = isset($params->unique_url) && !empty($params->unique_url) ? $params->unique_url : [];
        $count = 0;
        $slugify = new Slugify();
        $langKey = strtolower($lang['key']);
        if (!$type) {
            $data = $this->getSlugData($slug, $slugModel);
            $slug_all = isset($data['slug_all']) ? $data['slug_all'] : null;
            if ($slug_all && isset($slug_all[$lang['key']]) && $slug_all[$lang['key']]) {
                if (count($subModel)) {
                    foreach ($subModel as $key => $subData) {
                        $sub_slug_all = isset($subData['slug_all']) ? $subData['slug_all'] : null;
                        if ($sub_slug_all && isset($sub_slug_all[$lang['key']])) {
                            $url = $site . $langKey . '/' . $slug_all[$lang['key']] . '/' . $sub_slug_all[$lang['key']];
                            if ($slug == 'property' || $slug == 'property-rent') {
                                $url = $site . $langKey . '/' . $slug_all[$lang['key']] . '/';
                                $url = $url . (isset($subData['slug_all'][$lang['key']]) && !empty($subData['slug_all'][$lang['key']]) ? $slugify->slugify($subData['slug_all'][$lang['key']]) . '_' : 'property_') . $subData['id'];
                            }
                            if ($commercialProperty) {
                                $url = $sub_slug_all[$lang['key']];
                            }
                            if (!in_array($url, $unique_url)) {
                                $html .= '<url>';
                                $html .= '<loc>' . $url . '</loc>';
                                $html .= '<lastmod>' . date('Y-m-d') . '</lastmod>';
                                if ($slug == 'property' || $slug == 'property-rent') {
                                    $html .= '<changefreq>weekly</changefreq>';
                                    $html .= '<priority>0.8</priority>';
                                } elseif ($slug == 'blog-detail') {
                                    $html .= '<changefreq>weekly</changefreq>';
                                    $html .= '<priority>0.7</priority>';
                                } else {
                                    $html .= '<changefreq>weekly</changefreq>';
                                    $html .= '<priority>0.8</priority>';
                                }
                                $html .= '</url>';
                                $unique_url[] = $params->unique_url[] = $url;
                                App::instance('params', $params);
                            }
                        } else {
                            if ($slug == 'property' || $slug == 'property-rent') {
                                $url = $site . $langKey . '/' . $slug_all[$lang['key']] . '/';
                                $url = $url . 'property_' . $subData['id'];
                            }
                            if ($commercialProperty) {
                                $url = $slug_all[$lang['key']];
                            }
                            if (isset($url) && !in_array($url, $unique_url)) {
                                $html .= '<url>';
                                $html .= '<loc>' . $url . '</loc>';
                                $html .= '<lastmod>' . date('Y-m-d') . '</lastmod>';
                                if ($slug == 'property' || $slug == 'property-rent') {
                                    $html .= '<changefreq>weekly</changefreq>';
                                    $html .= '<priority>0.8</priority>';
                                }
                                $html .= '</url>';
                                $unique_url[] = $params->unique_url[] = $url;
                                App::instance('params', $params);
                            }
                        }
                    }
                } else {
                    $url = $site . $langKey . '/' . $slug_all[$lang['key']];
                    if ($slug == 'home') {
                        $url = $site . $langKey;
                    }
                    if (!in_array($url, $unique_url)) {
                        $html .= '<url>';
                        $html .= '<loc>' . $url . '</loc>';
                        $html .= '<lastmod>' . date('Y-m-d') . '</lastmod>';
                        if ($slug == 'home') {
                            $html .= '<changefreq>daily</changefreq>';
                            $html .= '<priority>1</priority>';
                        } else {
                            $html .= '<changefreq>monthly</changefreq>';
                            $html .= '<priority>0.6</priority>';
                        }
                        $html .= '</url>';
                        $unique_url[] = $params->unique_url[] = $url;
                        App::instance('params', $params);
                    }
                }
            }
            return $html;
        }
        if ($type) {
            $dataArray = $this->getTypeData($slug, $slugModel);

            foreach ($dataArray as $data) {
                $slug_all = isset($data['slug_all']) ? $data['slug_all'] : null;
                if ($slug_all && isset($slug_all[$lang['key']]) && $slug_all[$lang['key']]) {

                    $url = $site . $langKey . '/' . $slug_all[$lang['key']];
                    if (!in_array($url, $unique_url)) {
                        $html .= '<url>';
                        $html .= '<loc>' . $url . '</loc>';
                        $html .= '<lastmod>' . date('Y-m-d') . '</lastmod>';
                        $html .= '<changefreq>monthly</changefreq>';
                        $html .= '<priority>0.9</priority>';
                        $html .= '</url>';

                        $unique_url[] = $params->unique_url[] = $url;
                        App::instance('params', $params);
                    }
                }
            }
            return $html;
        }
    }

    function getSlugData($slug, $array)
    {
        foreach ($array as $key => $val) {
            if ($val['slug'] === $slug) {
                return $val;
            }
        }
        return null;
    }

    function getTypeData($slug, $array)
    {
        $dataArray = [];
        foreach ($array as $key => $val) {
            if ($val['type'] === $slug) {
                $dataArray[] = $val;
            }
        }
        return $dataArray;
    }

    function getAllBlogs()
    {
        $blogs = [];
        $data = Cms::postTypes('post', null, null, 10, false, ['cache' => false, 'page' => 0]);
        if (isset($data[0]["totalCount"]) && !empty($data[0]["totalCount"])) {
            $pages = ceil((isset($data[0]["totalCount"]) && !empty($data[0]["totalCount"]) ? $data[0]["totalCount"] : 0) / 10);
        } else {
            $pages = ceil((isset($data) && !empty($data) ? count($data) : 0) / 10);
        }
        $p = 0;
        while ($p <= $pages) {
            $blogs = array_merge($blogs, $data);
            $p++;
            $data = Cms::postTypes('post', null, null, 10, false, ['cache' => false, 'page' => $p]);
        }
        return $blogs;
    }

    function getAllPropertiesSale()
    {
        $properties_sale = [];
        $data_sale = Properties::findAll('&page=' . 0 . '&page_size=' . 1000 . '&sale=' . 1, false, false, ['set_query' => false]);
        $pages = ceil((isset($data_sale[0]['total_properties']) ? $data_sale[0]['total_properties'] : 0) / 1000);
        $p = 0;
        while ($p <= $pages) {
            $properties = array_merge($properties_sale, $data_sale);
            $p++;
            $data_sale = Properties::findAll('&page=' . $p . '&page_size=' . 1000 . '&sale=' . 1, false, false, ['set_query' => false]);
        }
        return $properties;
    }

    function getAllPropertiesRent()
    {
        $properties_rent = [];
        $data_rent = Properties::findAll('&page=' . 0 . '&page_size=' . 1000 . '&rent=' . 1, false, false, ['set_query' => false]);
        $pages = ceil((isset($data_rent[0]['total_properties']) ? $data_rent[0]['total_properties'] : 0) / 1000);
        $p = 0;
        while ($p <= $pages) {
            $properties = array_merge($properties_rent, $data_rent);
            $p++;
            $data_rent = Properties::findAll('&page=' . $p . '&page_size=' . 1000 . '&rent=' . 1, false, false, ['set_query' => false]);
        }
        return $properties;
    }

    function getSitemapIndex($site)
    {
        $langs = Cms::languages();
        foreach ($langs as $lang) {
            // if ($lang['key'] != 'ES') {
            $langg[] = $lang;
            // }
        }
        $html = '<?xml version="1.0" encoding="UTF-8"?>';
        $html .= '<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">';
        foreach ($langg as $lang) {
            $html .= '<sitemap>';
            $html .= '<loc>' . $site . 'sitemap-' . strtolower($lang['key']) . '.xml</loc>';
            $html .= '<lastmode>' . date('Y-m-d') . '</lastmode>';
            $html .= '</sitemap>';
        }
        $html .= '</sitemapindex>';

        return $html;
    }
    function getCommercialPropertiesSale()
    {
        $limit = 100;
        $postData = [
            "options" => [
                "page" => 1,
                "limit" => $limit,
                "populate" => [
                    ["path" => "property_attachments", "match" => ["document" => ['$ne' => true], "publish_status" => ['$ne' => false]]]
                ],
                "sort" => ["created_at" => 1, "reference" => 1]
            ],
            "query" => [
                "status" => ['$in' => ["Available", "Under Offer"]],
                "sale" => true,
                "archived" => ['$ne' => true],
                "remove_count" => true
            ]
        ];
        $properties = [];
        $data_sale = $this->getProperties($postData, 'sale');
        $pages = ceil((isset($data_sale['total']) ? $data_sale['total'] : 0) / $limit);
        $p = 1;
        while ($p <= $pages) {
            $properties = array_merge($properties, (isset($data_sale["properties"]) && !empty($data_sale["properties"]) ? $data_sale["properties"] : []));
            $p++;
            $postData['options']['page'] = $p;
            $data_sale = $this->getProperties($postData, 'sale');
        }

        return $properties;
    }
    function getCommercialPropertiesRent()
    {
        $limit = 100;
        $postData = [
            "options" => [
                "page" => 1,
                "limit" => $limit,
                "populate" => [
                    ["path" => "property_attachments", "match" => ["document" => ['$ne' => true], "publish_status" => ['$ne' => false]]]
                ],
                "sort" => ["created_at" => 1, "reference" => 1]
            ],
            "query" => [
                "status" => ['$in' => ["Available", "Under Offer"]],
                "rent" => true,
                "lt_rental" => true,
                "archived" => ['$ne' => true],
                "remove_count" => true
            ]
        ];
        $properties = [];
        $data_sale = $this->getProperties($postData, "rent");
        $pages = ceil((isset($data_sale['total']) ? $data_sale['total'] : 0) / $limit);
        $p = 1;
        while ($p <= $pages) {
            $properties = array_merge($properties, (isset($data_sale["properties"]) && !empty($data_sale["properties"]) ? $data_sale["properties"] : []));
            $p++;
            $postData['options']['page'] = $p;
            $data_sale = $this->getProperties($postData, "rent");
        }

        return $properties;
    }
    function getCommercialPropertiesHoliday()
    {
        $limit = 100;
        $postData = [
            "options" => [
                "page" => 1,
                "limit" => $limit,
                "populate" => [
                    ["path" => "property_attachments", "match" => ["document" => ['$ne' => true], "publish_status" => ['$ne' => false]]]
                ],
                "sort" => ["created_at" => 1, "reference" => 1]
            ],
            "query" => [
                "status" => ['$in' => ["Available", "Under Offer"]],
                "rent" => true,
                "st_rental" => true,
                "archived" => ['$ne' => true],
                "remove_count" => true
            ]
        ];
        $properties = [];
        $data_sale = $this->getProperties($postData, 'holiday');
        $pages = ceil((isset($data_sale['total']) ? $data_sale['total'] : 0) / $limit);
        $p = 1;
        while ($p <= $pages) {
            $properties = array_merge($properties, (isset($data_sale["properties"]) && !empty($data_sale["properties"]) ? $data_sale["properties"] : []));
            $p++;
            $postData['options']['page'] = $p;
            $data_sale = $this->getProperties($postData, 'holiday');
        }

        return $properties;
    }
    function getCommercialPropertiesDevelopment()
    {
        $limit = 100;
        $postData = [
            "options" => [
                "page" => 1,
                "limit" => $limit,
                "populate" => [
                    ["path" => "property_attachments", "match" => ["document" => ['$ne' => true], "publish_status" => ['$ne' => false]]]
                ],
                "sort" => ["created_at" => 1, "reference" => 1]
            ],
            "query" => [
                "status" => ['$in' => ["Available", "Under Offer"]],
                "project" => true,
                "archived" => ['$ne' => true],
                "remove_count" => true
            ]
        ];
        $properties = [];
        $data_sale = $this->getProperties($postData, 'development');
        $pages = ceil((isset($data_sale['total']) ? $data_sale['total'] : 0) / $limit);
        $p = 1;
        while ($p <= $pages) {
            $properties = array_merge($properties, (isset($data_sale["properties"]) && !empty($data_sale["properties"]) ? $data_sale["properties"] : []));
            $p++;
            $postData['options']['page'] = $p;
            $data_sale = $this->getProperties($postData, 'development');
        }
        return $properties;
    }

    function getProperties($post_data, $type = "sale")
    {
        $node_url = config('params.node_url') . 'commercial_properties?user=' . config('params.user');

        $response = Http::withHeaders([
                'Content-Type' => 'application/json',
                'Cache-Control' => 'no-cache',
                'Content-Length' => strlen(json_encode($post_data))
            ])->post($node_url, $post_data)->json();
        $properties = [];

        if (isset($response) && isset($response['docs'])) {
            foreach ($response['docs'] as $property) {
                $properties[] = self::formateProperty($property, [], $type);
            }
        }
        // $response['docs'] = $properties;
        return ["properties" => $properties, "total" => isset($response['total']) && !empty($response['total']) ? $response['total'] : 0];
    }

    public static function formateProperty($property, $set_options = [], $type = "sale")
    {
        $langg = array();
        $langs = Cms::languages();
        foreach ($langs as $lang) {
            $langg[] = isset($lang["key"]) && !empty($lang["key"]) ? $lang["key"] : '';
        }
        $f_property = [];
        if (isset($property['external_reference'])) {
            $f_property['external_reference'] = $property['external_reference'];
        }
        $agency = '';
        if (isset($property['agency']) && !empty($property['agency'])) {
            $agency = $property['agency'];
            $f_property['agency'] = $property['agency'];
        }
        if (isset($property['from_residential']) && !empty($property['from_residential'])) {
            $f_property['from_residential'] = $property['from_residential'];
        }
        if (isset($property['shared_categories']) && !empty($property['shared_categories'])) {
            $f_property['shared_categories'] = $property['shared_categories'];
        }
        if (isset($property['property_name']) && !empty($property['property_name'])) {
            $f_property['property_name'] = $property['property_name'];
        }
        if (isset($property['own']) && !empty($property['own'])) {
            $f_property['own'] = $property['own'];
        }
        if (isset($property['price_on_demand'])) {
            $f_property['price_on_demand'] = $property['price_on_demand'];
        }
        if (isset($property['agency_data']['commercial_name']) && !empty($property['agency_data']['commercial_name'])) {
            $f_property['agency_name'] = $property['agency_data']['commercial_name'];
        }
        if (isset($property['listing_agency_data']['commercial_name']) && !empty($property['listing_agency_data']['commercial_name'])) {
            $f_property['agency_name'] = $property['listing_agency_data']['commercial_name'];
        }
        if (isset($property['agency_data']['agency_email']) && !empty($property['agency_data']['agency_email'])) {
            $f_property['agency_email'] = $property['agency_data']['agency_email'];
        }
        if (isset($property['listing_agency_data']['agency_email']) && !empty($property['listing_agency_data']['agency_email'])) {
            $f_property['agency_email'] = $property['listing_agency_data']['agency_email'];
        }
        if (isset($property['private_info_object'][$agency]['cadastral_numbers'][0]['cadastral_number']) && !empty($property['private_info_object'][$agency]['cadastral_numbers'][0]['cadastral_number'])) {
            $f_property['cadastral_number'] = $property['private_info_object'][$agency]['cadastral_numbers'][0]['cadastral_number'];
        }
        if (isset($property['_id'])) {
            $f_property['_id'] = $property['_id'];
        }
        if (isset($property['reference'])) {
            $f_property['id'] = $property['reference'];
        }
        if (isset($property['agency_data']['logo']['name']) && !empty($property['agency_data']['logo']['name'])) {
            $f_property['agency_logo'] = 'https://images.optima-crm.com/agencies/' . (isset($property['agency_data']['_id']) ? $property['agency_data']['_id'] : '') . '/' . (isset($property['agency_data']['logo']['name']) ? $property['agency_data']['logo']['name'] : '');
        }
        if (isset($property['listing_agency_data']['logo']['name']) && !empty($property['listing_agency_data']['logo']['name'])) {
            $f_property['agency_logo'] = 'https://images.optima-crm.com/companies/' . (isset($property['listing_agency_data']['_id']) ? $property['listing_agency_data']['_id'] : '') . '/' . (isset($property['listing_agency_data']['logo']['name']) ? $property['listing_agency_data']['logo']['name'] : '');
            $f_property['compnay_id'] = isset($property['listing_agency_data']['_id']) ? $property['listing_agency_data']['_id'] : '';
        }

        if (isset($property['property_urls']) && !empty($property['property_urls'])) {
            foreach ($langg as $key => $value) {
                if ($type == 'rent') {
                    $f_property['slug_all'][strtoupper($value)] = isset($property['property_urls']['rent_url'][strtolower($value)]) ? $property['property_urls']['rent_url'][strtolower($value)] : "";
                } else if ($type == 'holiday') {
                    $rent_detail = self::getSlugByTagName('Rent Details', strtoupper($value));
                    $holiday_detail = self::getSlugByTagName('Holiday Details', strtoupper($value));
                    $f_property['slug_all'][strtoupper($value)] = isset($property['property_urls']['rent_url'][strtolower($value)]) ? str_replace($rent_detail, $holiday_detail, $property['property_urls']['rent_url'][strtolower($value)]) : "";
                } else if ($type == 'development') {
                    $sale_detail = self::getSlugByTagName('Sale Details', strtoupper($value));
                    $development_detail = self::getSlugByTagName('New Development Details', strtoupper($value));
                    $f_property['slug_all'][strtoupper($value)] = isset($property['property_urls']['sale_url'][strtolower($value)]) ? str_replace($sale_detail, $development_detail, $property['property_urls']['sale_url'][strtolower($value)]) : "";
                }else {
                    $f_property['slug_all'][strtoupper($value)] = isset($property['property_urls']['sale_url'][strtolower($value)]) ? $property['property_urls']['sale_url'][strtolower($value)] : "";
                }
            }
        }
        return $f_property;
    }

    public static function getSlugByTagName($tag, $lang = "EN")
    {
        $file_data = Cms::getSlugs();

        foreach ($file_data as $data) {
            if (isset($data['tags'][0]) && $data['tags'][0] == $tag) {
                return isset($data['slug'][$lang]) ? $data['slug'][$lang] : $data['slug']['EN'];
            }
        }
        return 'tag-not-found';
    }
}
