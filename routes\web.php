<?php

use App\Http\Controllers\SiteController;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Route;

Route::get('/', function() {
    return redirect('/'.App::getLocale());
});

Route::group(['prefix' => '{locale}', 'middleware' => 'locale'], function() {
    Route::get('/', [SiteController::class, 'Index']);
    Route::post('contact-us/store', [SiteController::class, 'contactUsStore'])->name('contact.store');
    Route::get('skip-cache', [SiteController::class, 'skipCache']);
});
