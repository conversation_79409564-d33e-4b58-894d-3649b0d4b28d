<?php

namespace App\Helpers;

use Daxit\OptimaClass\Components\Sitehelper;
use Daxit\OptimaClass\Components\Urlhelper;
use Daxit\OptimaClass\Helpers\Cms;
use Daxit\OptimaClass\Helpers\Dropdowns;
use Daxit\OptimaClass\Service\ParamsContainer;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\URL;

class Helper
{
    public static function get_languages_dropdown($object)
    {
        $property = $object->has('property') ? $object->get('property') : [];
        $development = $object->has('development') ? $object->get('development') : [];
        $post = $object->has('post') ? $object->get('post') : [];
        $page_data = $object->has('page_data') ? $object->get('page_data') : [];

        $page_slugs = array_slice(request()->segments(), 1);

        $page_slug = isset($page_slugs[0]) && !empty($page_slugs) ? $page_slugs[0] : '';

        App::instance('params', new ParamsContainer(["slug" => $page_slug]));

        $languages = Sitehelper::get_languages();
        $cmsModels = Cms::Slugs('page');

        $html = "<div class='dropdown-menu'>";

        foreach ($languages as $language) {
            $slug = isset($page_data['slug_all'][$language['key']]) ? $page_data['slug_all'][$language['key']] : (isset($page_data['slug_all']['EN']) && $page_data['slug_all']['EN'] != '' ? $page_data['slug_all']['EN'] : '');

            if ($development) {
                $title = Urlhelper::getDevelopmentTitle($development, $language['key']);
                $slug .= '/' . $title;
            } elseif ($post) {
                $title = Urlhelper::getPostTitle($post, $language['key']);
                $slug .= '/' . $title;
            }

            if (isset($page_data['slug_all']['EN']) && ($page_data['slug_all']['EN'] == 'home' || $page_data['slug_all']['EN'] == 'index')) {
                $url = ['language' => strtolower($language['key'])];
            } else {
                $url = ['language' => strtolower($language['key']), 'slug' => $slug];
            }

            $get_params = request()->query() ? request()->query() : [];

            if (isset($cmsModels) && !empty($cmsModels)) {
                foreach ($cmsModels as $model) {
                    if (isset($model['type']) && ($model['type'] == 'LocationsGroup' || $model['type'] == 'PropertyTypes') && $slug == $model['slug']) {
                        $get_params = [];
                    }
                }
            }

            unset($get_params['params']['st_rental'], $get_params['params']['pagename']);

            $url_to = Urlhelper::buildUrl(array_merge($url, ['params' => $get_params]));

            if ($property) {
                $url_to = Urlhelper::getCommercialPropertyUrl($property, strtolower($language['key']));
            }

            if (strtolower($language['key']) != strtolower(App::getLocale())) {
                $html .= '<a class=dropdown-item "' . (strtolower($language['key']) == strtolower(App::getLocale()) ? "active" : "") . '" href="' . URL::to($url_to) . '">' . $language['title'] . '</a>';
            }
        }

        $html .= "</div>";

        return $html;
    }

    public static function paginate(array $items, $page, int $perPage = 15, array $options = []): LengthAwarePaginator
    {
        $collection = collect(isset($items['docs']) && !empty($items['docs']) ? $items['docs'] : []);

        $options = array_merge([
            'path' => LengthAwarePaginator::resolveCurrentPath(),
            'query' => array_filter(request()->query())
        ], $options);

        return new LengthAwarePaginator(
            $collection,
            isset($items['total']) && !empty($items['total']) ? $items['total'] : 0,
            $perPage,
            $page,
            $options
        );
    }

    public static function getlocationGroups(): array
    {
        $locationGroups = Dropdowns::locationGroups();

        if(isset($locationGroups) && !empty($locationGroups)) {
            return array_filter(array_map(function ($locationGroup) {
                return isset($locationGroup['key_system']) ? $locationGroup['key_system'] : '';
            }, $locationGroups));
        } else {
            return [];
        }
    }
}

